<template>
  <main class="urine-time-section">
    <header class="urine-time-header">
      <h3 class="urine-time-title">체중</h3>
      <span>총 {{ totalDaysRecord }}일 기록</span>
    </header>
    <section>
      <nav>
        <div class="urine-time-count">
          <div class="urine-time-text">
            <span class="urine-time-number">{{ totalUrinesTimes }}</span>
            회
          </div>
        </div>
      </nav>
    </section>
  </main>
</template>

<script>
export default {
  name: "ReportUrineTime",

  props: {
    urinesTimes: {
      type: Array,
      // [morning, lunch, evening]
      default: () => [0, 0, 0],
      required: true,
      validator: (value) => value.length === 3,
    },

    selectedDate: {
      type: String,
      required: true,
      default: () => {
        const date = new Date();

        return `${date.getFullYear()}-${date.getMonth() + 1}`;
      },
    },
  },

  computed: {
    totalDaysRecord() {
      if (this.urinesTimes.length === 0) return 0;

      const date = this.selectedDate;
      const [year, month] = date.split("-").map(Number);
      const lastDay = new Date(year, month, 0).getDate();

      return lastDay;
    },

    totalUrinesTimes() {
      return this.calculateTotal(this.urinesTimes);
    },

    averageUrinesTimes() {
      return this.calculateAverage(this.urinesTimes);
    },
  },

  methods: {
    calculateTotal(array, defaultValue = 0) {
      if (!Array.isArray(array) || array.length === 0) {
        return defaultValue;
      }

      return array.reduce((acc, cur) => {
        const num = Number(cur);
        return acc + (isNaN(num) ? 0 : num);
      }, 0);
    },

    calculateAverage(array, defaultValue = 0) {
      if (!Array.isArray(array) || array.length === 0) {
        return defaultValue;
      }

      const total = this.calculateTotal(array);
      return Math.round(total / array.length);
    },
  },
};
</script>

<style lang="scss" scoped>
.urine-time-section {
  width: 100% !important;
  padding: 30px;
  display: flex;
  flex-direction: column;
  align-content: flex-start;

  > section > nav {
    display: flex;
    justify-content: center;
  }
}

.urine-time-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 30px;
}
</style>
