<template>
  <main class="urine-time-section">
    <header class="urine-time-header">
      <h3 class="urine-time-title">소변 횟수</h3>
      <span>총 {{ totalDaysRecord }}일 기록</span>
    </header>
    <section>
      <article>
        <div class="urine-time-count">
          <p class="urine-time-text">
            한달 평균 하루 배뇨 횟수는 {{ averageUrinesTimes }}회 입니다. 이번달
            전체 배뇨 횟수는 총 {{ totalUrinesTimes }}회 입니다.
          </p>
        </div>

        <!-- 원형 차트 추가 -->
        <div class="chart-container">
          <DynamicCircleChart
            :urinesTimes="urinesTimes"
            :labels="['오전', '오후', '저녁']"
            :colors="['#A8E6CF', '#888888', '#FFD93D']"
            @update:urinesTimes="updateUrinesTimes"
          />
        </div>
      </article>
    </section>
  </main>
</template>

<script>
// DynamicCircleChart 컴포넌트를 render 함수로 변환
const DynamicCircleChart = {
  name: "DynamicCircleChart",
  props: {
    urinesTimes: {
      type: Array,
      default: () => [0, 0, 0],
    },
    labels: {
      type: Array,
      default: () => ["오전", "오후", "저녁"],
    },
    colors: {
      type: Array,
      default: () => ["#A8E6CF", "#888888", "#FFD93D"],
    },
  },
  computed: {
    data() {
      return this.urinesTimes.map((value, index) => ({
        label: this.labels[index] || `Category ${index + 1}`,
        value: value || 0,
        color: this.colors[index] || "#cccccc",
      }));
    },
    positions() {
      return this.calculatePositions(this.data);
    },
  },
  methods: {
    // 원의 크기 계산 함수 (최소 크기 40, 최대 크기 120)
    getCircleSize(value) {
      if (value === 0) return 60; // 기본 크기
      // 소변 횟수에 따른 크기 조정 (1회당 6px 증가, 최대 120px)
      return Math.max(40, Math.min(120, value * 6 + 40));
    },

    // 원들의 위치 계산
    calculatePositions(data) {
      const centerX = 150;
      const centerY = 100;

      if (data.every((item) => item.value === 0)) {
        // 모든 값이 0일 때는 겹치게 배치
        return data.map(() => ({
          x: centerX,
          y: centerY,
        }));
      }

      // 값이 있을 때는 겹치되 약간씩 오프셋 적용
      const offsets = [
        { x: -25, y: -15 },
        { x: 30, y: -8 },
        { x: -8, y: 30 },
      ];

      return data.map((_, index) => ({
        x: centerX + offsets[index].x,
        y: centerY + offsets[index].y,
      }));
    },

    updateValue(index, newValue) {
      const clampedValue = Math.max(0, newValue);
      // props 배열을 직접 수정하지 않고 이벤트로 부모에게 알림
      const updatedTimes = [...this.urinesTimes];
      updatedTimes[index] = clampedValue;
      this.$emit("update:urinesTimes", updatedTimes);
    },

    resetToZero() {
      const resetTimes = new Array(this.urinesTimes.length).fill(0);
      this.$emit("update:urinesTimes", resetTimes);
    },

    setExample() {
      const exampleValues = [8, 3, 5];
      this.$emit("update:urinesTimes", exampleValues);
    },
  },
  // template 대신 render 함수 사용
  render(h) {
    const { data, positions } = this;

    // SVG 내부 원소들 생성
    const svgElements = data.map((item, index) => {
      return h("g", { key: index }, [
        h("circle", {
          class: "circle",
          attrs: {
            cx: positions[index].x,
            cy: positions[index].y,
            r: this.getCircleSize(item.value) / 2,
            fill: item.color,
            opacity: item.value === 0 ? 0.3 : 0.8,
          },
        }),
        h(
          "text",
          {
            class: "circle-text",
            attrs: {
              x: positions[index].x,
              y: positions[index].y,
              "text-anchor": "middle",
              "dominant-baseline": "middle",
            },
          },
          `${item.value}회`
        ),
      ]);
    });

    // 컨트롤 패널 생성
    const controlItems = data.map((item, index) => {
      return h(
        "div",
        {
          class: "control-item",
          key: index,
        },
        [
          h("label", { class: "control-label" }, item.label),
          h("div", { class: "control-row" }, [
            h("div", {
              class: "color-indicator",
              style: { backgroundColor: item.color },
            }),
            h("input", {
              class: "slider",
              attrs: {
                type: "range",
                min: "0",
                max: "15",
                value: item.value,
              },
              on: {
                input: (e) => this.updateValue(index, parseInt(e.target.value)),
              },
            }),
            h("span", { class: "value-display" }, `${item.value}회`),
          ]),
        ]
      );
    });

    return h("div", { class: "dynamic-chart" }, [
      // SVG 차트
      h("div", { class: "chart-wrapper" }, [
        h(
          "svg",
          {
            attrs: {
              width: "300",
              height: "200",
              viewBox: "0 0 300 200",
            },
          },
          svgElements
        ),
      ]),

      // 컨트롤 패널
      h("div", { class: "control-panel" }, [
        h("div", { class: "controls-grid" }, controlItems),
        h("div", { class: "button-group" }, [
          h(
            "button",
            {
              class: "btn btn-primary",
              on: { click: this.setExample },
            },
            "예시 적용"
          ),
          h(
            "button",
            {
              class: "btn btn-secondary",
              on: { click: this.resetToZero },
            },
            "초기화"
          ),
        ]),
      ]),
    ]);
  },
};

export default {
  name: "ReportUrineTime",

  components: {
    DynamicCircleChart,
  },

  props: {
    urinesTimes: {
      type: Array,
      // [morning, lunch, evening]
      default: () => [0, 0, 0],
      required: true,
      validator: (value) => value.length === 3,
    },

    selectedDate: {
      type: String,
      required: true,
      default: () => {
        const date = new Date();

        return `${date.getFullYear()}-${date.getMonth() + 1}`;
      },
    },
  },

  computed: {
    totalDaysRecord() {
      if (this.urinesTimes.length === 0) return 0;

      const date = this.selectedDate;
      const [year, month] = date.split("-").map(Number);
      const lastDay = new Date(year, month, 0).getDate();

      return lastDay;
    },

    totalUrinesTimes() {
      return this.calculateTotal(this.urinesTimes);
    },

    averageUrinesTimes() {
      return this.calculateAverage(this.urinesTimes);
    },
  },

  methods: {
    calculateTotal(array, defaultValue = 0) {
      if (!Array.isArray(array) || array.length === 0) {
        return defaultValue;
      }

      console.log(array);

      return array.reduce((acc, cur) => {
        const num = Number(cur);
        return acc + (isNaN(num) ? 0 : num);
      }, 0);
    },

    calculateAverage(array, defaultValue = 0) {
      if (!Array.isArray(array) || array.length === 0) {
        return defaultValue;
      }

      const total = this.calculateTotal(array);
      return Math.round(total / array.length);
    },

    updateUrinesTimes(newTimes) {
      // 부모 컴포넌트로 변경사항 전달
      this.$emit("update:urinesTimes", newTimes);
    },
  },
};
</script>

<style lang="scss" scoped>
.urine-time-section {
  width: 100% !important;
  padding: 30px;
  display: flex;
  flex-direction: column;
  align-content: flex-start;

  > section > nav {
    display: flex;
    justify-content: center;
  }
}

.urine-time-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 30px;
}

.urine-time-text {
  font-weight: 500;
  font-size: 14px;
  color: black;
  text-align: left;
  margin-bottom: 20px;
}

.chart-container {
  margin-top: 20px;
}

// DynamicCircleChart 스타일
.dynamic-chart {
  background: #f8f9fa;
  border-radius: 8px;
  padding: 20px;
}

.chart-wrapper {
  text-align: center;
  margin-bottom: 20px;
}

.circle {
  transition: all 0.5s ease-in-out;
}

.circle-text {
  font-size: 14px;
  font-weight: 600;
  fill: #000;
}

.control-panel {
  background: white;
  border-radius: 6px;
  padding: 15px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.controls-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 12px;
  margin-bottom: 15px;
}

.control-item {
  display: flex;
  flex-direction: column;
  gap: 6px;
}

.control-label {
  font-size: 0.8rem;
  font-weight: 500;
  color: #374151;
}

.control-row {
  display: flex;
  align-items: center;
  gap: 8px;
}

.color-indicator {
  width: 12px;
  height: 12px;
  border-radius: 50%;
  flex-shrink: 0;
}

.slider {
  flex: 1;
  height: 4px;
  border-radius: 2px;
  background: #d1d5db;
  outline: none;
  cursor: pointer;
}

.value-display {
  font-family: monospace;
  font-size: 0.75rem;
  min-width: 30px;
  text-align: right;
  color: #666;
}

.button-group {
  display: flex;
  justify-content: center;
  gap: 10px;
}

.btn {
  padding: 6px 12px;
  border: none;
  border-radius: 4px;
  font-size: 0.75rem;
  cursor: pointer;
  transition: background-color 0.2s;
}

.btn-primary {
  background-color: #3b82f6;
  color: white;

  &:hover {
    background-color: #2563eb;
  }
}

.btn-secondary {
  background-color: #6b7280;
  color: white;

  &:hover {
    background-color: #4b5563;
  }
}
</style>
