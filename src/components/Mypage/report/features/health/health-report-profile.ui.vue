<template>
  <main class="profile-wrapper">
    <header class="report-header">
      <nav>
        <!-- 날짜 컴포넌트 -->
        <p class="report-date" @click="handleOpenModal">
          {{ showReportDate }}
          <img
            :src="arrowImage"
            alt="arrow-ic"
            class="down-arrow"
            loading="lazy"
          />
        </p>
        <span
          >{{ getUserName }}님, <br />{{ selectedMonth }}월 심 건강 점수는
          <br />
          {{
        }}</span>
      </nav>
      <nav>profile img</nav>
    </header>
  </main>
</template>

<script>
export default {
  name: "HealthReportProfile",

  props: {
    selectedDate: {
      type: [String, Date],
      default: "",
      required: true,
      validator: (value) => {
        // 형식: `{{YEAR}}-{{MONTH}}`
        return true;
      },
    },
    cymScore: {
      type: [Number, String],
      required: true,
      default: 0,
      validator: (value) => {
        let val;
        if (typeof val === "string") {
          val = Number(value);
        }
        if (val >= 0 && val <= 100) return true;

        console.warn("score is not the numerical standard (0-100)");
        return false;
      },
    },
  },

  data() {
    return {
      arrowImage: require("@/assets/images_assets/icons/arrow-ic.png"),
    };
  },

  watch: {
    selectedDate: {
      handler(newDate) {
        // props가 변경될 때마다 data 업데이트
        this.updateDisplayData(newDate);
      },
      immediate: true, // 컴포넌트 생성 시에도 실행
    },
  },

  computed: {
    // 포멧팅 함수
    showReportDate() {
      if (!this.selectedDate) return "";

      const dateStr = this.selectedDate.toString();
      const [year, month] = dateStr.split("-");

      if (!year || !month) return "";

      // 해당 월의 마지막 날 구하기
      const lastDay = new Date(parseInt(year), parseInt(month), 0).getDate();

      // 월을 2자리로 포맷팅
      const formattedMonth = month.padStart(2, "0");

      return `${year}-${formattedMonth}-01~${formattedMonth}-${lastDay}`;
    },

    // selectedDate에서 month만 추출
    selectedMonth() {
      if (!this.selectedDate) return null;

      const [_, month] = this.selectedDate.split("-");
      return month ? parseInt(month) : null;
    },

    // localStorage에 저장되어있는 username 가져오기
    getUserName() {
      const username = localStorage.getItem("username") ?? "";

      return username;
    },

    getCymScore() {
      return this.cymScore;
    },
  },

  methods: {
    // 기간 설정 모달 열어주는 메서드
    handleOpenModal() {
      this.$store.commit("SET_SELECT_DATE_MODAL", true);
    },

    // props 변경에 따른 데이터 업데이트
    updateDisplayData(selectedDate) {
      if (!selectedDate) {
        this.displayData = null;
        return;
      }

      // selectedDate에 따라 다른 데이터 설정
      const [year, month] = selectedDate.split("-");

      this.displayData = {
        year: parseInt(year),
        month: parseInt(month),
        period: this.showReportDate, // computed 값 사용
        // 여기에 selectedDate에 따른 다른 데이터들 추가
        reportType: `${year}년 ${month}월 리포트`,
        dataCount: Math.floor(Math.random() * 100), // 예시 데이터
      };

      // API 호출이나 다른 로직도 여기서 실행 가능
      this.fetchReportData(selectedDate);
    },

    // 리포트 데이터 가져오기
    fetchReportData(selectedDate) {
      console.log(`Fetching data for ${selectedDate}`);
      // API 호출 로직
      this.reportData = {
        // selectedDate에 따른 실제 데이터
      };
    },
  },
};
</script>

<style lang="scss" scoped>
.profile-wrapper {
  width: 100% !important;
  padding: 30px;
  display: flex;
  flex-direction: column;
  align-content: flex-start;
}

.report-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.report-date {
  font-size: 16px;
  font-weight: 500;
  cursor: pointer;
  color: #000000;
}

.down-arrow {
  width: 6px;
  height: 11px;
  transform: rotate(90deg) translateY(-5px);
}
</style>
