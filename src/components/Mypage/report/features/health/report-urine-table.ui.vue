<template>
  <div class="urine-test-results">
    <div
      v-for="resultType in filteredResults"
      :key="resultType.key"
      class="result-section"
    >
      <div class="result-header">
        <div :class="['icon-container', resultType.colorClass]">
          <img :src="resultType.icon" :alt="resultType.key" />
        </div>
        <span class="result-title">{{ resultType.title }}</span>
        <span class="total-count">총 {{ resultType.data.total }}회</span>
      </div>
      <div class="result-items">
        <div
          v-for="item in getFilteredItems(resultType.data)"
          :key="item.key"
          class="result-item"
        >
          <span class="item-name">{{ item.name }}</span>
          <span class="item-count">{{ item.count }}회</span>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: "UrineTestResults",
  props: {
    urineData: {
      type: Object,
      required: true,
      default: () => ({
        totalCount: 0,
        negative: {
          total: 0,
          blood: 0,
          ph: 0,
          protein: 0,
          glucose: 0,
          ketone: 0,
        },
        good: {
          total: 0,
          blood: 0,
          ph: 0,
          protein: 0,
          glucose: 0,
          ketone: 0,
        },
        warning: {
          total: 0,
          blood: 0,
          ph: 0,
          protein: 0,
          glucose: 0,
          ketone: 0,
        },
        caution: {
          total: 0,
          blood: 0,
          ph: 0,
          protein: 0,
          glucose: 0,
          ketone: 0,
        },
        danger: {
          total: 0,
          blood: 0,
          ph: 0,
          protein: 0,
          glucose: 0,
          ketone: 0,
        },
      }),
    },
  },
  data() {
    return {
      urineKeys: {
        ph: this.$t("ph").replace(/\s/g, ""),
        blood: this.$t("blood").replace(/\s/g, ""),
        protein: this.$t("protein").replace(/\s/g, ""),
        glucose: this.$t("glucose").replace(/\s/g, ""),
        ketone: this.$t("ketone").replace(/\s/g, ""),
      },
      droplets: {
        negative: require("@/assets/images/droplets/ketone_good.png"),
        good: require("@/assets/images/droplets/ketone_good.png"),
        warning: require("@/assets/images/droplets/result_warning.png"),
        caution: require("@/assets/images/droplets/result_caution.png"),
        danger: require("@/assets/images/droplets/result_danger.png"),
      },
      resultTypes: [
        {
          key: "negative",
          title: "음성",
          icon: this.droplets.negative,
          colorClass: "green",
        },
        {
          key: "good",
          title: "양성(+)",
          icon: this.droplets.good,
          colorClass: "yellow",
        },
        {
          key: "warning",
          title: "양성(++)",
          icon: this.droplets.warning,
          colorClass: "orange",
        },
        {
          key: "caution",
          title: "주의",
          icon: this.droplets.caution,
          colorClass: "red",
        },
        {
          key: "danger",
          title: "위험",
          icon: this.droplets.danger,
          colorClass: "dark-red",
        },
      ],
      itemTypes: [
        { key: "blood", name: "잠혈" },
        { key: "ph", name: "pH" },
        { key: "protein", name: "단백질" },
        { key: "glucose", name: "포도당" },
        { key: "ketone", name: "케톤" },
      ],
    };
  },
  computed: {
    filteredResults() {
      return this.resultTypes
        .map((resultType) => ({
          ...resultType,
          data: this.urineData[resultType.key],
        }))
        .filter((result) => result.data.total > 0);
    },
  },
  methods: {
    getFilteredItems(data) {
      return this.itemTypes
        .map((item) => ({
          ...item,
          count: data[item.key],
        }))
        .filter((item) => item.count > 0);
    },
  },
};
</script>

<style scoped>
.urine-test-results {
  background-color: #f5f5f5;
  padding: 20px;
  border-radius: 8px;
  font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif;
}

.result-section {
  margin-bottom: 30px;
}

.result-header {
  display: flex;
  align-items: center;
  margin-bottom: 15px;
}

.icon-container {
  width: 50px;
  height: 50px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 15px;
  font-size: 24px;
}

.icon-container.green {
  background-color: #4caf50;
}

.icon-container.yellow {
  background-color: #ffc107;
}

.icon-container.orange {
  background-color: #ff9800;
}

.icon-container.red {
  background-color: #f44336;
}

.icon-container.dark-red {
  background-color: #d32f2f;
}

.smile-icon,
.neutral-icon,
.sad-icon,
.icon {
  color: white;
  font-size: 24px;
}

.result-title {
  font-size: 24px;
  font-weight: bold;
  color: #333;
  margin-right: auto;
}

.total-count {
  font-size: 20px;
  font-weight: bold;
  color: #333;
}

.result-items {
  margin-left: 65px;
}

.result-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 8px 0;
  border-bottom: 1px solid #e0e0e0;
}

.result-item:last-child {
  border-bottom: none;
}

.item-name {
  font-size: 18px;
  color: #666;
}

.item-count {
  font-size: 18px;
  font-weight: bold;
  color: #333;
}
</style>
