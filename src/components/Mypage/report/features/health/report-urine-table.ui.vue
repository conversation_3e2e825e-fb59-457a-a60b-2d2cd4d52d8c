<template>
  <div class="urine-test-results">
    <div
      v-for="resultType in filteredResults"
      :key="resultType.key"
      class="result-section"
    >
      <div class="result-header">
        <div :class="['icon-container']">
          <img
            :src="resultType.icon"
            :alt="resultType.key"
            class="result-icon"
            loading="lazy"
          />
        </div>
      </div>
      <div class="result-items">
        <div class="result-item">
          <span class="result-title">{{ resultType.title }}</span>
          <span class="total-count">총 {{ resultType.data.total }}회</span>
        </div>

        <div
          v-for="item in getFilteredItems(resultType.data)"
          :key="item.key"
          class="result-item"
        >
          <span class="item-name">{{ item.name }}</span>
          <span class="item-count">{{ item.count }}회</span>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: "UrineTestResults",
  props: {
    urineData: {
      type: Object,
      required: true,
      default: () => ({
        totalCount: 0,
        negative: {
          total: 0,
          blood: 0,
          ph: 0,
          protein: 0,
          glucose: 0,
          ketone: 0,
        },
        good: {
          total: 0,
          blood: 0,
          ph: 0,
          protein: 0,
          glucose: 0,
          ketone: 0,
        },
        warning: {
          total: 0,
          blood: 0,
          ph: 0,
          protein: 0,
          glucose: 0,
          ketone: 0,
        },
        caution: {
          total: 0,
          blood: 0,
          ph: 0,
          protein: 0,
          glucose: 0,
          ketone: 0,
        },
        danger: {
          total: 0,
          blood: 0,
          ph: 0,
          protein: 0,
          glucose: 0,
          ketone: 0,
        },
      }),
    },
  },
  data() {
    return {
      droplets: {
        negative: require("@/assets/images/droplets/result_normal.png"),
        good: require("@/assets/images/droplets/ketone_good.png"),
        warning: require("@/assets/images/droplets/result_warning.png"),
        caution: require("@/assets/images/droplets/result_caution.png"),
        danger: require("@/assets/images/droplets/result_danger.png"),
      },
      itemTypes: [
        { key: "blood", name: "잠혈" },
        { key: "ph", name: "pH" },
        { key: "protein", name: "단백질" },
        { key: "glucose", name: "포도당" },
        { key: "ketone", name: "케톤" },
      ],
    };
  },

  computed: {
    // data 내에서는 this.$t() 같은 인스턴스 메서드 참조 불가
    urineKeys() {
      return {
        ph: this.$t("ph").replace(/\s/g, ""),
        blood: this.$t("blood").replace(/\s/g, ""),
        protein: this.$t("protein").replace(/\s/g, ""),
        glucose: this.$t("glucose").replace(/\s/g, ""),
        ketone: this.$t("ketone").replace(/\s/g, ""),
      };
    },

    // data 내에서는 다른 data 변수 참조 불가
    resultTypes() {
      return [
        {
          key: "negative",
          title: "음성",
          icon: this.droplets.negative,
          colorClass: "green",
        },
        {
          key: "good",
          title: "양성(+)",
          icon: this.droplets.good,
          colorClass: "yellow",
        },
        {
          key: "warning",
          title: "양성(++)",
          icon: this.droplets.warning,
          colorClass: "orange",
        },
        {
          key: "caution",
          title: "주의",
          icon: this.droplets.caution,
          colorClass: "red",
        },
        {
          key: "danger",
          title: "위험",
          icon: this.droplets.danger,
          colorClass: "dark-red",
        },
      ];
    },

    filteredResults() {
      return this.resultTypes
        .map((resultType) => ({
          ...resultType,
          data: this.urineData[resultType.key],
        }))
        .filter((result) => result.data.total > 0);
    },
  },
  methods: {
    getFilteredItems(data) {
      return this.itemTypes
        .map((item) => ({
          ...item,
          count: data[item.key],
        }))
        .filter((item) => item.count > 0);
    },
  },
};
</script>

<style lang="scss" scoped>
.urine-test-results {
  background-color: #f5f5f5;
  padding: 30px;
  border-radius: 8px;
}

.result-section {
  display: flex;
  justify-content: space-between;
  margin-bottom: 30px;
}

.result-section:last-child {
  margin-bottom: 0;
}

.result-header {
  height: fit-content;
  display: flex;
  align-items: center;
  margin-bottom: 15px;
}

.smile-icon,
.neutral-icon,
.sad-icon,
.icon {
  color: white;
  font-size: 24px;
}

.result-title {
  font-size: 16px;
  font-weight: bold;
  color: #333;
}

.total-count {
  font-size: 16px;
  font-weight: bold;
  color: #333;
}

.result-items {
  width: 80%;
  align-self: flex-end;
  font-weight: 500;
}

.result-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 5px;
}

.result-item:last-child {
  border-bottom: none;
}

.item-name {
  font-size: 12px;
  color: #666;
  font-weight: 500;
}

.item-count {
  font-size: 12px;
  font-weight: 500;
  color: #333;
}

.result-icon {
  width: 32px;
  height: 41px;
}
</style>
