<template>
  <div class="urine-table-wrapper"></div>
</template>

<script>
export default {
  name: "ReportUrineTable",

  props: {
    urineData: {
      type: Object,
      required: true,
      default: () => ({
        totalCount: 0,
        negative: {
          total: 0,
          blood: 0,
          ph: 0,
          protein: 0,
          glucose: 0,
          ketone: 0,
        },
        good: {
          total: 0,
          blood: 0,
          ph: 0,
          protein: 0,
          glucose: 0,
          ketone: 0,
        },
        warning: {
          total: 0,
          blood: 0,
          ph: 0,
          protein: 0,
          glucose: 0,
          ketone: 0,
        },
        caution: {
          total: 0,
          blood: 0,
          ph: 0,
          protein: 0,
          glucose: 0,
          ketone: 0,
        },
        danger: {
          total: 0,
          blood: 0,
          ph: 0,
          protein: 0,
          glucose: 0,
          ketone: 0,
        },
      }),
    },
  },

  data() {
    return {
      urineKeys: {
        ph: this.$t("ph").replace(/\s/g, ""),
        blood: this.$t("blood").replace(/\s/g, ""),
        protein: this.$t("protein").replace(/\s/g, ""),
        glucose: this.$t("glucose").replace(/\s/g, ""),
        ketone: this.$t("ketone").replace(/\s/g, ""),
      },
    };
  },

  mounted() {
    console.log(this.urineData);
  },

  methods: {},
};
</script>

<style lang="scss" scoped>
.urine-table-wrapper {
  width: 100%;
  height: auto;
  background: #f8f8f8;
  padding: 30px;
  border-radius: 10px;
}
</style>
