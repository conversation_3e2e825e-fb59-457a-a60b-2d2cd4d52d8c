<template>
  <main class="urine-wrapper">
    <!---->
    <header class="upper-container">
      <h3 class="urine-title">소변 검사</h3>
      <nav class="urine-total-wrapper">
        <span class="urine-total">총 {{ urines.totalCount }}회 진행</span>
        <span class="total-count-text">5항목 x {{ urines.totalCount }}회</span>
      </nav>
    </header>

    <section>
      <p class="manage-text">{{ managementText }}</p>
      <ReportUrineChart :isKetosis="isKetosis" :series="chartSeries" />
    </section>
  </main>
</template>

<script>
import ReportUrineChart from "../../ui/report-urine-chart.ui.vue";

import { countUrineTest } from "./utils/format-urine.util";
import { setUrineSeries, setUrineChartSeries } from "./utils/urine-chart.util";

export default {
  name: "ReportUrineTest",

  props: {
    urineData: {
      type: Object,
      required: true,
      default: () => {
        return {
          urineCount: {
            morningUrine: 0,
            lunchUrine: 0,
            eveningUrine: 0,
          },
          urines: [],
        };
      },
    },
  },

  components: {
    ReportUrineChart,
  },

  data() {
    return {
      urines: {},
      chartSeries: {},
    };
  },

  computed: {
    managementText() {
      if (!this.urines) {
        return "주기적인 검사가 건강 관리의 시작! 오늘부터 소변 검사를 진행해보세요. 🚀";
      }

      return "앞으로 잠혈, 단백질, 포도당 항목 수치를 주의해서 관리해주세요.";
    },

    // urineData에서 isKetoneMode 추출
    isKetosis() {
      return this.urineData && this.urineData.isKetoneMode
        ? this.urineData.isKetoneMode
        : false;
    },
  },

  watch: {
    urineData: {
      handler(newData) {
        if (newData && newData.urines) {
          this.countUrineTest();
        }
      },
      deep: true,
      immediate: true,
    },
  },

  mounted() {
    this.countUrineTest();
  },

  methods: {
    countUrineTest() {
      try {
        this.urines = countUrineTest(this.urineData?.urines);

        this.urineTests = setUrineSeries(this.urines);

        this.chartSeries = setUrineChartSeries(this.urineTests);
      } catch (error) {
        console.error(error);
      }
    },
  },
};
</script>

<style lang="scss" scoped>
.urine-wrapper {
  width: 100% !important;
  padding: 30px;
  display: flex;
  flex-direction: column;
  align-content: flex-start;
}

.urine-title {
  font-size: 16px;
  font-weight: bold;
  text-align: left;
}

.urine-total {
  font-weight: 500;
  font-size: 16px;
}

.upper-container {
  display: flex;
  justify-content: space-between;
  margin-bottom: 15px;
}

.total-count-text {
  font-size: 12px;
  color: black;
  font-weight: 500;
}

.urine-total-wrapper {
  display: flex;
  flex-direction: column;
  align-items: flex-end;
}

.manage-text {
  font-weight: 500;
  font-size: 14px;
  color: black;
  text-align: left;
}
</style>
