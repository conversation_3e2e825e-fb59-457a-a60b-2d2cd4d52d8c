<template>
  <main>report-urine-test</main>
</template>

<script>
import { countUrineTest } from "./utils/format-urine.util";

export default {
  name: "ReportUrineTest",

  props: {
    urineData: {
      type: Object,
      required: true,
      default: () => {
        return {
          urineCount: {
            morningUrine: 0,
            lunchUrine: 0,
            eveningUrine: 0,
          },
          urines: [],
        };
      },
    },
  },

  data() {
    return {};
  },

  mounted() {
    this.countUrineTest();
  },

  methods: {
    countUrineTest() {
      try {
        const result = countUrineTest(this.urineData?.urines);
        console.log(result);
      } catch (error) {
        console.error(error);
      }
    },
  },
};
</script>

<style lang="scss" scoped></style>
