/**
 * @description =negative와 positive를 합하는 메서드
 * @param {object} urines
 * @param {object} urines.blood
 * @param {object} urines.ph
 * @param {object} urines.protein
 * @param {object} urines.glucose
 * @param {object} urines.ketone
 * @returns {Array} series
 */
function setUrineSeries(urines) {
  const result = {
    negative: urines.negative?.total || 0,
    good: urines.good?.total || 0, // 케토시스 좋음
    positive:
      (urines.warning?.total || 0) +
      (urines.caution?.total || 0) +
      (urines.danger?.total || 0),
  };

  return result;
}

function setUrineChartSeries(data) {
  return [
    {
      name: "음성",
      data: [data.negative?.total || 0],
    },
    {
      name: "양성",
      data: [
        (data.warning?.total || 0) +
          (data.caution?.total || 0) +
          (data.danger?.total || 0),
      ],
    },
    {
      name: "케톤 좋음",
      data: [data.good?.total || 0],
    },
  ];
}

export { setUrineSeries, setUrineChartSeries };
