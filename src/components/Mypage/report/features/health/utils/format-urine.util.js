import { getUrineScore } from "./urine-score.utils";

/**
 * 
 * @param {Array} urines 
 * ex: {
      "id": 1033,
      "blood": 1,
      "ph": 2,
      "protein": 1,
      "glucose": 1,
      "ketone": 1,
      "createdAt": "2025-05-26T07:52:46.802Z",
      "updatedAt": "2025-05-26T07:52:46.802Z"
    },

 */
function countUrineTest(urines) {
  console.log(urines);
  if (urines.length === 0) {
    throw Error("no urine test result");
  }

  const result = {
    totalCount: urines.length,
    negative: {
      total: 0,
      blood: 0,
      ph: 0,
      protein: 0,
      glucose: 0,
      ketone: 0,
    }, // normal
    good: {
      total: 0,
      blood: 0,
      ph: 0,
      protein: 0,
      glucose: 0,
      ketone: 0,
    }, // ketosis mode
    warning: {
      total: 0,
      blood: 0,
      ph: 0,
      protein: 0,
      glucose: 0,
      ketone: 0,
    },
    caution: {
      total: 0,
      blood: 0,
      ph: 0,
      protein: 0,
      glucose: 0,
      ketone: 0,
    },
    danger: {
      total: 0,
      blood: 0,
      ph: 0,
      protein: 0,
      glucose: 0,
      ketone: 0,
    },
  };

  urines.forEach((urine) => {
    for (const [key, value] of Object.entries(urine)) {
      if (key === "id" || key === "createdAt" || key === "updatedAt") continue;
      const score = getUrineScore(
        key,
        value,
        urines.isKetoneMode ? urines.isKetoneMode : false
      );
      result[score][key]++;
      result[score].total++;
    }
  });

  return result;
}

export { countUrineTest };
