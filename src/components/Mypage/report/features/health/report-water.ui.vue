<template>
  <main class="water-section">
    <header class="water-header">
      <h3>수분 섭취량</h3>
      <span>총 {{ totalDaysRecord }}일 기록</span>
    </header>
    <section>
      <article>
        <p class="water-summary">
          한달 평균 하루 수분 섭취량은 {{ averageWaterRecord }} 입니다. 이번달
          전체 수분 섭취량은 총 {{ totalWaterRecord }} 입니다.
        </p>
      </article>
      <article class="glass-container">
        <div class="glass-mask-container">
          <img
            src="@/assets/images_assets/images/glass_mask.png"
            alt="glass_mask"
          />
        </div>
        <div class="circle">
          <div class="wave" ref="water"></div>
          <div class="value-txt" ref="valueTxt">{{ totalWaterRecord }}</div>
        </div>
      </article>
    </section>
  </main>
</template>

<script>
import { formatWaterVolume } from "./utils/water.utils";

export default {
  name: "ReportWater",

  props: {
    selectedDate: {
      type: String,
      required: true,
      default: () => {
        const date = new Date();

        return `${date.getFullYear()}-${date.getMonth() + 1}`;
      },
    },

    averageWater: {
      type: Number,
      required: true,
      default: 0,
    },

    totalWater: {
      type: Number,
      required: true,
      default: 0,
    },
  },

  data() {
    return {
      topPosition: 255,
    };
  },

  computed: {
    totalDaysRecord() {
      const date = this.selectedDate;
      const [year, month] = date.split("-").map(Number);
      const lastDay = new Date(year, month, 0).getDate();

      return lastDay;
    },

    totalWaterRecord() {
      return formatWaterVolume(this.totalWater);
    },

    averageWaterRecord() {
      return formatWaterVolume(this.averageWater);
    },
  },

  mounted() {
    const waterValue = this.totalWater;
    this.topPosition = this.computedTopPosition(waterValue);

    this.$refs.water.style.top = `${this.topPosition}px`;

    if (waterValue >= 1100) {
      this.$refs.valueTxt.style.color = "#ffffff";
    }
  },

  methods: {
    computedTopPosition(val) {
      const increaseValue = 255 / 20;
      const currentVolume = val > 2000 ? 2000 / 100 : val / 100;
      const calculateVolume = currentVolume * increaseValue;
      const topPosition = 255 - (calculateVolume + 12.5);

      return topPosition;
    },
  },
};
</script>

<style lang="scss" scoped>
.water-section {
  width: 100% !important;
  padding: 30px;
  display: flex;
  flex-direction: column;
  align-content: flex-start;
}

.water-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 30px;
}

.water-summary {
  font-size: 14px;
  font-weight: 500;
  color: #000000;
  text-align: start;
  letter-spacing: -3%;
}

.glass-mask-container {
  max-width: 134px;
  display: flex;
  justify-content: center;
  overflow: hidden;

  > img {
    width: 100%;
  }
}

.glass-container {
  width: 100%;
  display: flex;
  justify-content: center;
}

.circle {
  background-color: #f8f8f8;
  position: absolute;
  width: 134px;
  height: 134px;
  left: 50%;
  transform: translateX(-50%);
  overflow: hidden;
  transition: all 1s;
}

.wave {
  position: absolute;
  width: 200px;
  height: 200px;
  border-radius: 43%;
  left: 50%;
  transform: translateX(-10%);
  background: #41d8e6;
  animation: wave 6s infinite linear;
  transition: all 1s;
  opacity: 0.7;
}

.value-txt {
  position: absolute;
  font-family: GilroyBold;
  top: 55%;
  left: 50%;
  transform: translate(-50%, -50%);
  transition: all 0.4s;
  font-size: 26px;
  color: #41d8e6;
  z-index: 99;
}

@keyframes wave {
  0% {
    transform: translate(-50%) rotate(-90deg);
  }

  100% {
    transform: translate(-50%) rotate(360deg);
  }
}
</style>
