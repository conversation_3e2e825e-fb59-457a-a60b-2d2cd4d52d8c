<template>
  <div
    class="bmi-info-modal"
    v-show="isModalOpen"
    @click="closeModal"
    @touchmove="preventBackgroundScroll"
    @wheel="preventBackgroundScroll"
    ref="modal"
  >
    estsetset
  </div>
</template>

<script>
export default {
  name: "BMIInfoModal",

  data() {
    return { isModalOpen: false };
  },

  watch: {
    modalState(newVal) {
      this.isModalOpen = newVal;
    },

    isModalOpen(newVal) {
      if (newVal) {
        this.scrollY = window.scrollY;
        document.body.style.overflow = "hidden";
        document.body.style.position = "fixed";
        document.body.style.width = "100%";
        document.body.style.top = `-${this.scrollY}px`;
      } else {
        document.body.style.overflow = "";
        document.body.style.position = "";
        document.body.style.width = "";
        document.body.style.top = "";
        window.scrollTo(0, this.scrollY);
      }
    },
  },

  computed: {
    modalState() {
      return this.$store.state.showBmiInfoModal || false;
    },
  },

  methods: {
    closeModal() {
      this.isModalOpen = false;
      this.$store.commit("SET_BMI_INFO_MODAL", false);
    },
    preventBackgroundScroll(e) {
      if (e.target === this.$refs.modal) {
        e.preventDefault();
      }
    },
  },

  beforeDestroy() {
    if (this.isModalOpen) {
      document.body.style.overflow = "";
      document.body.style.position = "";
      document.body.style.width = "";
      document.body.style.top = "";
      window.scrollTo(0, this.scrollY);
    }
  },
};
</script>

<style lang="scss" scoped>
.bmi-info-modal {
  $modal-background: rgba(0, 0, 0, 0.5);
  width: 100vw;
  height: 100vh;
  max-width: 100vw;
  max-height: 100vh;
  background: $modal-background;
  position: fixed;
  top: 0;
  left: 0;
  margin: 0;
  padding: 0;
  border: none;
  z-index: 99999;
  display: flex;
  flex-direction: column;
  justify-content: center;
}
</style>
