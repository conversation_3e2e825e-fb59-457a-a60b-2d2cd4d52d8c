<template>
  <header>
    <fixed-header :threshold="50">
      <div class="nav-bar">
        <div
          class="fixed-header"
          :class="isIos ? 'nav-space' : 'nav-space-android'"
        >
          <div>
            <div class="history-header_nav">
              <router-link to="/home"
                ><v-icon>$back_btn_bold</v-icon></router-link
              >
            </div>
          </div>
        </div>
      </div>
    </fixed-header>
    <div class="care-header">
      <div
        :class="isIos ? 'care-header__wrapper' : 'care-header__wrapper-android'"
      >
        <div class="care-header_nav">
          <router-link to="/home"><v-icon>$back_btn_bold</v-icon></router-link>
        </div>
        <div class="care-header_title">
          <span
            class="care-header__title--ko"
            :class="lang === 'ko' ? '' : 'en-title'"
            >{{ setTitle }}</span
          >
          <span class="care-header__title--en">Cym Report</span>
        </div>
      </div>
    </div>
  </header>
</template>

<script>
import { defineComponent } from "vue";
import FixedHeader from "vue-fixed-header";

export default defineComponent({
  name: "CymReportHeader",

  components: {
    FixedHeader,
  },
});
</script>

<style lang="scss" scoped>
.nav-bar.vue-fixed-header--isFixed .fixed-header {
  background-color: #c9f4f8;
  display: block !important;
  position: fixed;
  top: 0;
  left: 50%;
  transform: translateX(-50%);
  width: 100%;
  max-width: 450px;
  z-index: 999;
}

.care-header {
  margin-bottom: 15px;
}

.nav-bar.vue-fixed-header .fixed-header {
  display: none;
}

.nav-space {
  padding: 55px 0 15px 10px;
}
.nav-space-android {
  padding: 20px 0 15px 10px;
}

.history-header_nav {
  display: flex;
  justify-content: flex-start;
  padding-left: 20px;
}

.care-header__wrapper {
  padding-top: 65px;
  padding-left: 30px;
}
.care-header__wrapper-android {
  padding-top: 30px;
  padding-left: 30px;
}

.care-header_nav {
  text-align: left;
}

.care-header_title {
  text-align: left;
  font-size: 28px;
  padding-top: 10px;
  line-height: 30px;
}

.care-header_subtitle {
  text-align: left;
  font-size: 18px;
  font-weight: 500;
  padding-top: 22px;
  padding-bottom: 10px;
  letter-spacing: -0.03em;
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding-right: 25px;
}

.care-header__title--ko {
  line-height: 30px;
  font-weight: 700;
  font-size: 30px;
  margin-bottom: 5px;
}

.care-header__title--en {
  line-height: 30px;
  font-family: GilroyBold;
  font-size: 36px;
}
</style>
