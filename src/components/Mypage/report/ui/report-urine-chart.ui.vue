<template>
  <div class="urine-chart-container">
    <!-- 커스텀 차트 바 -->
    <div class="chart-bar-wrapper">
      <div class="chart-bar">
        <div
          class="bar-segment negative"
          :style="{ width: negativePercentage + '%' }"
        ></div>
        <div
          class="bar-segment ketone"
          :style="{ width: ketonePercentage + '%' }"
        ></div>
        <div
          class="bar-segment positive"
          :style="{ width: positivePercentage + '%' }"
        ></div>
      </div>
    </div>

    <!-- 범례 -->
    <div class="legend">
      <div class="legend-item">
        <div
          :class="[
            'legend-color',
            negativeCount === 0 ? 'negative-zero-color' : 'negative-color',
          ]"
        ></div>
        <span class="legend-text">음성</span>
        <span class="legend-count">{{ negativeCount }}회</span>
      </div>
      <div class="legend-item" v-if="isKetosis">
        <div class="legend-color ketone-color"></div>
        <span class="legend-text">케톤 중음</span>
        <span class="legend-count">{{ ketoneCount }}회</span>
      </div>
      <div class="legend-item">
        <div class="legend-color positive-color"></div>
        <span class="legend-text">양성</span>
        <span class="legend-count">{{ positiveCount }}회</span>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: "ReportUrineChart",

  props: {
    series: {
      type: Array,
      default: () => [
        {
          name: "음성",
          data: [0],
        },
        {
          name: "양성",
          data: [0],
        },
      ],
    },
    isKetosis: {
      type: Boolean,
      default: false,
    },
  },

  computed: {
    // 안전하게 series를 배열로 변환
    safeSeries() {
      if (!Array.isArray(this.series)) {
        console.warn("series is not an array, using default values");
        return [
          { name: "음성", data: [0] },
          { name: "양성", data: [0] },
        ];
      }
      return this.series;
    },

    negativeCount() {
      const negativeSeries = this.safeSeries.find((s) => s.name === "음성");
      return negativeSeries && Array.isArray(negativeSeries.data)
        ? negativeSeries.data[0] || 0
        : 0;
    },

    ketoneCount() {
      const ketoneSeries = this.safeSeries.find((s) => s.name === "케톤 중음");
      return ketoneSeries && Array.isArray(ketoneSeries.data)
        ? ketoneSeries.data[0] || 0
        : 0;
    },

    positiveCount() {
      const positiveSeries = this.safeSeries.find((s) => s.name === "양성");
      return positiveSeries && Array.isArray(positiveSeries.data)
        ? positiveSeries.data[0] || 0
        : 0;
    },

    totalCount() {
      return this.negativeCount + this.ketoneCount + this.positiveCount;
    },

    negativePercentage() {
      return this.totalCount > 0
        ? (this.negativeCount / this.totalCount) * 100
        : 0;
    },

    ketonePercentage() {
      return this.totalCount > 0
        ? (this.ketoneCount / this.totalCount) * 100
        : 0;
    },

    positivePercentage() {
      return this.totalCount > 0
        ? (this.positiveCount / this.totalCount) * 100
        : 0;
    },
  },

  mounted() {},
};
</script>

<style lang="scss" scoped>
.urine-chart-container {
  padding: 0 0 15px 0;
}

.chart-bar-wrapper {
  margin-bottom: 15px;
}

.chart-bar {
  display: flex;
  height: 20px;
  border-radius: 20px;
  overflow: hidden;
  background-color: #f0f0f0;
}

.bar-segment {
  height: 100%;
  transition: width 1.5s ease-in-out;
}

.bar-segment.negative {
  background-color: #22c55e;
  border-top-left-radius: 20px;
  border-bottom-left-radius: 20px;
}

.bar-segment.negative-zero {
  background-color: #dadada;
  border-top-left-radius: 20px;
  border-bottom-left-radius: 20px;
}

.bar-segment.ketone {
  background-color: #bb0090;
}

.bar-segment.positive {
  background-color: #dadada;
  border-top-right-radius: 20px;
  border-bottom-right-radius: 20px;
}

.legend {
  display: flex;
  justify-content: space-between;
  align-items: center;
  flex-wrap: wrap;
  gap: 20px;
}

.legend-item {
  display: flex;
  align-items: center;
  gap: 8px;
}

.legend-color {
  width: 10px;
  height: 10px;
  border-radius: 50%;
}

.negative-color {
  background-color: #22c55e;
}

.negative-zero-color {
  background-color: #dadada;
}

.ketone-color {
  background-color: #bb0090;
}

.positive-color {
  background-color: #dadada;
}

.legend-text {
  font-size: 12px;
  font-weight: 500;
  color: #333;
}

.legend-count {
  font-size: 12px;
  font-weight: 500;
  color: #333;
}

/* 반응형 스타일 */
@media (max-width: 768px) {
  .legend {
    gap: 15px;
  }

  .legend-item {
    justify-content: center;
  }
}
</style>
