<template>
  <div>
    <nav class="tab-container">
      <v-tabs
        v-model="tab"
        color="#C9F4F8"
        class="history-tabs pa-0"
        center-active
        grow
        @change="changeTabs"
        mobile-breakpoint="xs"
        slider-color="#41D8E6"
        slider-size="2"
        height="40px"
      >
        <v-tab v-for="item in tabList" :key="item.id" :href="item.href">
          {{ item.name }}
        </v-tab>
      </v-tabs>
    </nav>
  </div>
</template>

<script>
export default {
  name: "ReportCategory",

  props: {
    tab: {
      type: [String, Number],
      default: 0,
      required: true,
    },
    tabList: {
      type: Array,
      required: true,
      validator: function (value) {
        // 배열의 모든 요소가 객체인지 확인
        return value.every((item) => typeof item === "object" && item.name);
      },
    },
  },

  data() {
    return {};
  },

  methods: {
    changeTabs(newTab) {
      this.$emit("change", newTab);
    },
  },
};
</script>

<style lang="scss" scoped>
.tab-container {
  background: transparent !important;
}

.history-tabs {
  background: transparent;
}

// v-tabs 기본 배경색 제거
:deep(.v-tabs-bar) {
  background-color: transparent !important;
}

:deep(.theme--light.v-tabs > .v-tabs-bar) {
  background-color: transparent !important;
}

// 클릭 시 ripple effect 제거
:deep(.v-tab) {
  &::before {
    display: none !important;
  }
}

:deep(.v-ripple__container) {
  display: none !important;
}

// 선택된 탭 폰트 굵기 조정
:deep(.v-tab--active) {
  font-weight: bold !important; // 또는 bold
}

:deep(.v-tab) {
  font-weight: 500;
}

// v-tabs-slider height 조정
:deep(.v-tabs-slider) {
  height: 3px !important;
}
</style>
