<template>
  <div>
    <nav class="tab-container">
      <v-tabs
        v-model="tab"
        color="#C9F4F8"
        class="history-tabs pa-0"
        center-active
        grow
        @change="changeTabs"
        mobile-breakpoint="xs"
        slider-color="#41D8E6"
        slider-size="2"
        height="40px"
      >
        <v-tab v-for="item in tabList" :key="item.id" :href="item.href">
          {{ item.name }}
        </v-tab>
      </v-tabs>
    </nav>
    
    <v-tab-items v-model="tab">
      <v-tab-item v-for="item in tabList" :key="item.id" :value="item.href">
        <component :is="item.component" v-if="item.component" />
      </v-tab-item>
    </v-tab-items>
  </div>
</template>

<script>
export default {
  name: "ReportCategory",

  props: {
    tab: {
      type: [String, Number],
      default: 0,
      required: true,
    },
  },

  data() {
    return {
      tabList: [
        {
          id: 0,
          name: "건강리포트",
          href: "#health-report",
          component: "HealthReport", // 컴포넌트 이름
        },
        {
          id: 1,
          name: "케어 캘린더",
          href: "#care-calendar",
          component: "CareCalendar", // 컴포넌트 이름
        },
      ],
    };
  },
};
</script>

<style lang="scss" scoped>
.tab-container {
    background: transparent;
    
}
</style>
