<template>
  <dialog id="select-date-modal" class="select-date-modal" ref="modal">
    안녕하세요
  </dialog>
</template>

<script>
export default {
  name: "SelectDateModal",

  computed: {
    isOpen() {
      return this.$store.state.showSelectDateModal;
    },
  },

  methods: {},
};
</script>

<style lang="scss" scoped>
.select-date-modal {
  $modal-background: #000000;

  width: 100vw;
  height: 100vh;
  max-height: 100vh;
  background: $modal-background;
  position: fixed;
  top: 0;
  left: 0;
  z-index: 99999;
  opacity: 0.5;
}
</style>
