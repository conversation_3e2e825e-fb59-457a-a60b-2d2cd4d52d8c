<template>
  <div
    v-show="isModalOpen"
    id="select-date-modal"
    class="select-date-modal"
    ref="modal"
  >
    <div class="modal-item">hello world</div>
  </div>
</template>

<script>
export default {
  name: "SelectDateModal",

  data() {
    return {
      isModalOpen: false,
    };
  },

  computed: {
    modalState() {
      return this.$store.state.setting.showSelectDateModal || false;
    },
  },

  watch: {
    modalState(newVal) {
      this.isModalOpen = newVal;
    },
  },

  methods: {
    closeModal() {
      this.isModalOpen = false;
      this.$store.commit("SET_SELECT_DATE_MODAL", false);
    },
  },
};
</script>

<style lang="scss" scoped>
.select-date-modal {
  $modal-background: rgba(0, 0, 0, 0.5);

  width: 100vw;
  height: 100vh;
  max-width: 100vw;
  max-height: 100vh;
  background: $modal-background;
  position: fixed;
  top: 0;
  left: 0;
  margin: 0;
  padding: 0;
  border: none;
  z-index: 99999;

  // flexbox 설정으로 modal-item을 하단에 배치
  display: flex;
  flex-direction: column;
  justify-content: flex-end;
}

// dialog 태그의 기본 스타일 제거
.select-date-modal::backdrop {
  background: transparent;
}

.modal-item {
  width: 100%;
  background: white;
  justify-self: self-end;
}
</style>
mo
