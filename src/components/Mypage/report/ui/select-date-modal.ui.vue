<template>
  <transition name="modal-fade">
    <div
      v-if="isModalOpen"
      id="select-date-modal"
      class="select-date-modal"
      @click="closeModal"
      ref="modal"
    >
      <transition name="slide-up">
        <div v-if="isModalOpen" class="modal-item" @click.stop>
          <p>월 선택</p>
        </div>
      </transition>
    </div>
  </transition>
</template>

<script>
export default {
  name: "SelectDateModal",

  data() {
    return {
      isModalOpen: false,
    };
  },

  computed: {
    modalState() {
      return this.$store.state.setting.showSelectDateModal || false;
    },
  },

  watch: {
    modalState(newVal) {
      this.isModalOpen = newVal;
    },
  },

  methods: {
    closeModal() {
      this.isModalOpen = false;
      this.$store.commit("SET_SELECT_DATE_MODAL", false);
    },
  },
};
</script>

<style lang="scss" scoped>
.select-date-modal {
  $modal-background: rgba(0, 0, 0, 0.5);

  width: 100vw;
  height: 100vh;
  max-width: 100vw;
  max-height: 100vh;
  background: $modal-background;
  position: fixed;
  top: 0;
  left: 0;
  margin: 0;
  padding: 0;
  border: none;
  z-index: 99999;
  display: flex;
  flex-direction: column;
  justify-content: flex-end;
}

// dialog 태그의 기본 스타일 제거
.select-date-modal::backdrop {
  background: transparent;
}

// slide-up 애니메이션
.slide-up-enter-active,
.slide-up-leave-active {
  transition: transform 0.3s ease-out;
}

.slide-up-enter-from {
  transform: translateY(100%);
}

.slide-up-enter-to {
  transform: translateY(0);
}

.slide-up-leave-from {
  transform: translateY(0);
}

.slide-up-leave-to {
  transform: translateY(100%);
}

// 모달 배경 페이드 애니메이션
.modal-fade-enter-active,
.modal-fade-leave-active {
  transition: opacity 0.3s ease;
}

.modal-fade-enter-from,
.modal-fade-leave-to {
  opacity: 0;
}

.modal-item {
  width: 100%;
  background: white;
  padding: 30px;
  border-radius: 20px 20px 0 0;
  > p {
    text-align: start;
    color: #000000;
    font-weight: bold;
    font-size: 20px;
    letter-spacing: -3%;
    line-height: 25px;
  }
}
</style>
