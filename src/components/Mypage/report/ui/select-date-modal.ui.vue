<template>
  <div
    v-show="isModalOpen"
    id="select-date-modal"
    class="select-date-modal"
    @click="closeModal"
    @touchmove="preventBackgroundScroll"
    @wheel="preventBackgroundScroll"
    ref="modal"
  >
    <transition name="slide-up">
      <div v-if="isModalOpen" class="modal-item" @click.stop @touchmove.stop>
        <p>월 선택</p>
        <div class="picker-container">
          <div class="picker-column">
            <div
              class="picker-wrapper"
              ref="yearPicker"
              @scroll="updateYearFade"
            >
              <div
                v-for="(year, index) in availableYears"
                :key="year"
                :class="['picker-item', { active: selectedYear === year }]"
                :style="getItemStyle(index, 'year')"
                @click="selectYear(year)"
                :ref="`year-${index}`"
              >
                {{ year }}
              </div>
            </div>
            <div class="picker-center-line"></div>
          </div>
          <div class="picker-column">
            <div
              class="picker-wrapper"
              ref="monthPicker"
              @scroll="updateMonthFade"
            >
              <div
                v-for="(month, index) in availableMonths"
                :key="month"
                :class="['picker-item', { active: selectedMonth === month }]"
                :style="getItemStyle(index, 'month')"
                @click="selectMonth(month)"
                :ref="`month-${index}`"
              >
                {{ String(month).padStart(2, "0") }}
              </div>
            </div>
            <div class="picker-center-line"></div>
          </div>
        </div>
        <button class="confirm-button" @click="confirmSelection">확인</button>
      </div>
    </transition>
  </div>
</template>

<script>
export default {
  name: "SelectDateModal",
  data() {
    return {
      isModalOpen: false,
      selectedYear: new Date().getFullYear(),
      selectedMonth: new Date().getMonth() + 1,
      scrollY: 0,
      yearFadeStyles: {},
      monthFadeStyles: {},
    };
  },
  computed: {
    modalState() {
      return this.$store.state.setting.showSelectDateModal || false;
    },
    availableYears() {
      const currentYear = new Date().getFullYear();
      const years = [];
      // 2020년부터 현재 년도까지
      for (let year = 2020; year <= currentYear; year++) {
        years.push(year);
      }
      return years;
    },
    availableMonths() {
      const currentDate = new Date();
      const currentYear = currentDate.getFullYear();
      const currentMonth = currentDate.getMonth() + 1;

      if (this.selectedYear === currentYear) {
        // 현재 년도인 경우 현재 달 -1까지만 표시
        const months = [];
        for (let month = 1; month < currentMonth; month++) {
          months.push(month);
        }
        return months;
      } else {
        // 과거 년도인 경우 1월부터 12월까지 모두 표시
        return Array.from({ length: 12 }, (_, i) => i + 1);
      }
    },
  },
  watch: {
    modalState(newVal) {
      this.isModalOpen = newVal;
      if (newVal) {
        this.$nextTick(() => {
          this.scrollToSelected();
          this.updateYearFade();
          this.updateMonthFade();
        });
      }
    },
    isModalOpen(newVal) {
      if (newVal) {
        // 모달이 열릴 때 현재 스크롤 위치 저장 후 body 스크롤 막기
        this.scrollY = window.scrollY;
        document.body.style.overflow = "hidden";
        document.body.style.position = "fixed";
        document.body.style.width = "100%";
        document.body.style.top = `-${this.scrollY}px`;
      } else {
        // 모달이 닫힐 때 body 스크롤 복원
        document.body.style.overflow = "";
        document.body.style.position = "";
        document.body.style.width = "";
        document.body.style.top = "";
        window.scrollTo(0, this.scrollY);
      }
    },
    selectedYear() {
      // 년도가 변경되면 월 선택을 초기화
      if (
        this.availableMonths.length > 0 &&
        !this.availableMonths.includes(this.selectedMonth)
      ) {
        this.selectedMonth =
          this.availableMonths[this.availableMonths.length - 1];
      }
    },
  },
  methods: {
    closeModal() {
      this.isModalOpen = false;
      this.$store.commit("SET_SELECT_DATE_MODAL", false);
    },
    preventBackgroundScroll(e) {
      // 모달 배경에서만 스크롤 방지 (모달 내부는 제외)
      if (e.target === this.$refs.modal) {
        e.preventDefault();
      }
    },
    selectYear(year) {
      this.selectedYear = year;
      this.scrollToSelected();
    },
    selectMonth(month) {
      this.selectedMonth = month;
      this.scrollToSelected();
    },
    getItemStyle(index, type) {
      const styles =
        type === "year" ? this.yearFadeStyles : this.monthFadeStyles;
      return styles[index] || {};
    },
    updateYearFade() {
      this.$nextTick(() => {
        const picker = this.$refs.yearPicker;
        if (!picker) return;

        const pickerRect = picker.getBoundingClientRect();
        const centerY = pickerRect.top + pickerRect.height / 2;
        const styles = {};

        this.availableYears.forEach((year, index) => {
          const itemEl = picker.children[index];
          if (itemEl) {
            const itemRect = itemEl.getBoundingClientRect();
            const itemCenterY = itemRect.top + itemRect.height / 2;
            const distance = Math.abs(itemCenterY - centerY);
            const maxDistance = pickerRect.height / 2;
            const opacity = Math.max(0.2, 1 - (distance / maxDistance) * 0.8);

            styles[index] = {
              opacity: opacity,
              transform: `scale(${0.8 + opacity * 0.2})`,
            };

            // 가장 중앙에 가까운 항목을 선택된 항목으로 설정
            if (distance < 25) {
              // 25px 이내면 선택된 것으로 간주
              this.selectedYear = year;
            }
          }
        });

        this.yearFadeStyles = styles;
      });
    },
    updateMonthFade() {
      this.$nextTick(() => {
        const picker = this.$refs.monthPicker;
        if (!picker) return;

        const pickerRect = picker.getBoundingClientRect();
        const centerY = pickerRect.top + pickerRect.height / 2;
        const styles = {};

        this.availableMonths.forEach((month, index) => {
          const itemEl = picker.children[index];
          if (itemEl) {
            const itemRect = itemEl.getBoundingClientRect();
            const itemCenterY = itemRect.top + itemRect.height / 2;
            const distance = Math.abs(itemCenterY - centerY);
            const maxDistance = pickerRect.height / 2;
            const opacity = Math.max(0.2, 1 - (distance / maxDistance) * 0.8);

            styles[index] = {
              opacity: opacity,
              transform: `scale(${0.8 + opacity * 0.2})`,
            };

            // 가장 중앙에 가까운 항목을 선택된 항목으로 설정
            if (distance < 25) {
              // 25px 이내면 선택된 것으로 간주
              this.selectedMonth = month;
            }
          }
        });

        this.monthFadeStyles = styles;
      });
    },
    scrollToSelected() {
      this.$nextTick(() => {
        // 년도 스크롤
        const yearPicker = this.$refs.yearPicker;
        if (yearPicker) {
          const selectedYearIndex = this.availableYears.indexOf(
            this.selectedYear
          );
          if (selectedYearIndex >= 0) {
            const itemHeight = 50;
            const containerHeight = yearPicker.offsetHeight;
            const scrollTop =
              selectedYearIndex * itemHeight -
              containerHeight / 2 +
              itemHeight / 2;
            yearPicker.scrollTo({ top: scrollTop, behavior: "smooth" });
          }
        }

        // 월 스크롤
        const monthPicker = this.$refs.monthPicker;
        if (monthPicker) {
          const selectedMonthIndex = this.availableMonths.indexOf(
            this.selectedMonth
          );
          if (selectedMonthIndex >= 0) {
            const itemHeight = 50;
            const containerHeight = monthPicker.offsetHeight;
            const scrollTop =
              selectedMonthIndex * itemHeight -
              containerHeight / 2 +
              itemHeight / 2;
            monthPicker.scrollTo({ top: scrollTop, behavior: "smooth" });
          }
        }
      });
    },
    confirmSelection() {
      // 선택된 년도와 월을 부모 컴포넌트나 스토어에 전달
      this.$emit("date-selected", {
        year: this.selectedYear,
        month: this.selectedMonth,
      });

      // 또는 스토어에 저장
      this.$store.commit("SET_SELECTED_DATE", {
        year: this.selectedYear,
        month: this.selectedMonth,
      });

      this.closeModal();
    },
  },
  mounted() {
    // 초기 선택값을 가장 마지막 가능한 달로 설정
    const currentDate = new Date();
    const currentYear = currentDate.getFullYear();
    const currentMonth = currentDate.getMonth() + 1;

    // 현재 년도의 현재 달 -1을 기본값으로 설정
    this.selectedYear = currentYear;
    if (currentMonth > 1) {
      this.selectedMonth = currentMonth - 1; // 현재 달 -1
    } else {
      // 1월인 경우 작년 12월로 설정
      this.selectedYear = currentYear - 1;
      this.selectedMonth = 12;
    }
  },
  beforeDestroy() {
    // 컴포넌트가 파괴될 때 body 스크롤 복원
    if (this.isModalOpen) {
      document.body.style.overflow = "";
      document.body.style.position = "";
      document.body.style.width = "";
      document.body.style.top = "";
      window.scrollTo(0, this.scrollY);
    }
  },
};
</script>

<style lang="scss" scoped>
.select-date-modal {
  $modal-background: rgba(0, 0, 0, 0.5);
  width: 100vw;
  height: 100vh;
  max-width: 100vw;
  max-height: 100vh;
  background: $modal-background;
  position: fixed;
  top: 0;
  left: 0;
  margin: 0;
  padding: 0;
  border: none;
  z-index: 99999;
  display: flex;
  flex-direction: column;
  justify-content: flex-end;
}

.select-date-modal::backdrop {
  background: transparent;
}

.slide-up-enter-active {
  transition: transform 0.3s ease-out;
}

.slide-up-leave-active {
  transition: transform 0.3s ease-in;
}

.slide-up-enter-from {
  transform: translateY(100%);
}

.slide-up-enter-to {
  transform: translateY(0);
}

.slide-up-leave-from {
  transform: translateY(0);
}

.slide-up-leave-to {
  transform: translateY(100%);
}

.modal-item {
  width: 100%;
  background: white;
  padding: 30px;
  border-radius: 20px 20px 0 0;

  > p {
    text-align: start;
    color: #000000;
    font-weight: bold;
    font-size: 20px;
    letter-spacing: -3%;
    line-height: 25px;
    margin-bottom: 30px;
  }
}

.picker-container {
  display: flex;
  gap: 20px;
  margin-bottom: 30px;
}

.picker-column {
  flex: 1;
  display: flex;
  flex-direction: column;
  align-items: center;
  position: relative;
}

.picker-wrapper {
  width: 100px;
  height: 200px;
  overflow-y: auto;
  scroll-behavior: smooth;
  padding: 75px 0; // 위아래 여백으로 중앙 정렬 효과

  &::-webkit-scrollbar {
    display: none;
  }

  -ms-overflow-style: none;
  scrollbar-width: none;
}

.picker-center-line {
  position: absolute;
  top: 50%;
  left: 0;
  right: 0;
  height: 50px;
  transform: translateY(-50%);
  border-top: 1px solid #e0e0e0;
  border-bottom: 1px solid #e0e0e0;
  pointer-events: none;
  z-index: 1;
}

.picker-item {
  width: 100%;
  height: 50px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 18px;
  font-weight: 500;
  color: #000000;
  cursor: pointer;
  transition: all 0.1s ease;

  &.active {
    font-weight: bold;
    font-size: 20px;
  }
}

.confirm-button {
  width: 100%;
  height: 50px;
  background: #00bcd4;
  color: white;
  border: none;
  border-radius: 10px;
  font-size: 16px;
  font-weight: bold;
  cursor: pointer;
  transition: background-color 0.2s ease;

  &:hover {
    background: #00acc1;
  }

  &:active {
    background: #0097a7;
  }
}
</style>
