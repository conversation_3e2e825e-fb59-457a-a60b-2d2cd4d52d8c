import Vue from "vue";
import Vuex from "vuex";
import actions from "./actions/index.js";
import mutations from "./mutations/index.js";

Vue.use(Vuex);

export default new Vuex.Store({
  state: {
    accessToken: localStorage.getItem("auth") || "",
    isLogin: false,
    loginError: false,
    loginErrorMsg: "",
    password: "",
    surveyStatus: "",
    surveyModalOpen: false,
    overReqModalOpen: false,
    analysisWarningModalOpen: false,
    lastAnalysisTime: null,
    appType: "",
    deviceId: "",
    notiStatus: false,
    historyTabChanged: "",
    backBtnClicked: false,
    groupId: null,
    subjectId: null,
    snsUid: null,
    snsConnectionModal: false,
    homePopupVisible: true,

    setting: {
      showSelectDateModal: false,
      selectedDate: "",
      showBmiInfoModal: false,
    },

    care: {
      isShowBloodPressurePopup: false,
      recordModalVisible: false,
    },

    // join
    join: {
      account: "",
      password: "",
      marketingAllow: false,
      country: "",
      phone: "",
      email: "",
      userDetail: {
        nickname: "",
        sex: "",
        birth: "",
        height: 0,
        initialWeight: 0,
        targetWeight: 0,
        targetWater: 0,
      },
      health_info: {
        purpose: [],
        exercise: "",
        chronic: [],
        drinking: "",
        smoking: "",
      },
      meal_info: [],
      opinion: "",
    },

    // cym main
    urineTestResult: {
      cym702: [],
      blood: [],
      glucose: [],
      protein: [],
      ph: [],
      ketone: [],
    },
    cymRecentData: [],
    totalScore: 0,
    chartData: [],
    targetWater: 0,
    weight: { value: 0, createdAt: "0000.00.00" },
    careData: {
      weight: 0,
      water: "",
      pee: 0,
    },
    tabChange: false,
    activeDropletIdx: 4,
    mainchartIdx: null,
    showCompleteModal: false,
    username: "",
    userImg: "",
    phone: "",
    selectUser: 0,
    subUserId: 0,
    subUser: "",
    subUserGender: "",
    subUserBirth: "",
    subUserType: "",
    isBoat: true,

    // history data
    historyPage: 1,
    historyTotalPage: 5,
    historyLimit: 5,
    tooltipIndex: null,
    cymHistory: {
      cymAvg: "",
      cymScore: "",
      cymHistory: [],
      blood: [],
      glucose: [],
      protein: [],
      ph: [],
      ketone: [],
    },

    careHistory: {
      weightData: [],
      waterData: [],
      peeData: [],
      careDetailData: [],
    },

    openGuide: false,
    cardTitle: "",
    showCymscore: false,
    userImage: null,
    alreadySigned: false,
    isSuccess: false,
    isEnableTouch: false,
    month: 0,
    openKetoneEditModal: false,

    // solution
    showSelectModal: false,
    selectedFood: {},
    selectedFoodId: 0,
    clickTitle: false,
    showSelectBtn: false,
    selectionBtn: false,
    showRecordSelectModal: false,
    recordSelectType: "",
    recordSelectBtn: false,
    filterBtn: false,
    bookmarklist: [],
    mealplan: {
      breakfastlist: [],
      lunchlist: [],
      dinnerlist: [],
    },
    FoodAmountPercentage: [],
  },
  mutations,
  actions,
  getters: {
    loggedIn: (state) => state.isLogin,
    accessToken: (state) => state.isLogin,
    activeDropletIdx: (state) => state.activeDropletIdx,
    showCompleteModal: (state) => state.showCompleteModal,
    blood: (state) => state.urineTestResult.blood,
    glucose: (state) => state.urineTestResult.glucose,
    protein: (state) => state.urineTestResult.protein,
    ph: (state) => state.urineTestResult.ph,
    ketone: (state) => state.urineTestResult.ketone,
    cymScore: (state) => state.cymHistory.cymScore,
    cymAvg: (state) => state.cymHistory.cymAvg,
    tooltipIdx: (state) => state.tooltipIndex,
  },
  strict: process.env.NODE_ENV !== "production",
});
