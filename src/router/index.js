import Vue from "vue";
import VueRouter from "vue-router";

Vue.use(VueRouter);

const routes = [
  {
    path: "/",
    redirect: "/login",
  },
  {
    path: "/login",
    name: "Login",
    component: () =>
      import(/* webpackChunkName: "cym" */ "../pages/signin/Login.vue"),
    beforeEnter: (to, from, next) => {
      // console.log("BEFORE ENTER");

      const introChecked = localStorage.getItem("introChecked");

      if (from.path === "/intro") {
        next();
      } else {
        if (introChecked) {
          if (localStorage.getItem("auth")) {
            next("/home");
          } else {
            next();
          }
        } else {
          next("/notice");
        }
      }
    },
  },
  {
    path: "/notice",
    name: "Notice",
    component: () =>
      import(/* webpackChunkName: "cym" */ "../pages/Notice.vue"),
  },
  {
    path: "/intro",
    name: "Intro",
    component: () => import(/* webpackChunkName: "cym" */ "../pages/Intro.vue"),
  },

  {
    path: "/join",
    name: "Join",
    component: () =>
      import(/* webpackChunkName: "cym" */ "../pages/join/Join.vue"),
  },
  {
    path: "/find",
    name: "FindAccount",
    component: () =>
      import(
        /* webpackChunkName: "cym" */ "../pages/myaccount/FindAccount.vue"
      ),
  },
  {
    path: "/home",
    name: "Home",
    component: () =>
      import(/* webpackChunkName: "cym" */ "../pages/home/<USER>"),
    meta: { auth: true },
  },
  {
    path: "/survey",
    name: "Survey",
    component: () =>
      import(/* webpackChunkName: "cym" */ "../pages/survey/Survey.vue"),
    meta: { auth: true },
  },
  {
    path: "/home/<USER>",
    name: "History",
    component: () =>
      import(/* webpackChunkName: "cym" */ "../pages/history/History.vue"),
    meta: { auth: true },
  },
  {
    path: "/home/<USER>/woman",
    name: "Woman",
    component: () =>
      import(/* webpackChunkName: "cym" */ "../pages/care/WomanView.vue"),
    meta: { auth: true },
  },
  {
    path: "/home/<USER>/weight",
    name: "Weight",
    component: () =>
      import(/* webpackChunkName: "cym" */ "../pages/care/Weight.vue"),
    meta: { auth: true },
  },
  {
    path: "/home/<USER>/water",
    name: "Water",
    component: () =>
      import(/* webpackChunkName: "cym" */ "../pages/care/Water.vue"),
    meta: { auth: true },
  },
  {
    path: "/home/<USER>/pee",
    name: "pee",
    component: () =>
      import(/* webpackChunkName: "cym" */ "../pages/care/Pee.vue"),
    meta: { auth: true },
  },
  {
    path: "/home/<USER>/step",
    name: "step",
    component: () =>
      import(/* webpackChunkName: "cym" */ "../pages/care/StepCare.page.vue"),
    meta: { auth: true },
  },
  {
    path: "/home/<USER>/bloodPressure",
    name: "bloodPressure",
    component: () =>
      import(
        /* webpackChunkName: "cym" */ "../pages/care/BloodPressure.page.vue"
      ),
    meta: { auth: true },
  },
  {
    path: "/exam/intro",
    name: "ExamIntro",
    component: () =>
      import(/* webpackChunkName: "cym" */ "../pages/exam/ExamIntro.vue"),
    meta: { auth: true },
  },
  {
    path: "/exam/video",
    name: "ExamVideo",
    component: () =>
      import(/* webpackChunkName: "cym" */ "../pages/exam/ExamVideo.vue"),
    meta: { auth: true },
  },
  {
    path: "/exam/guide1",
    name: "ExamGuide1",
    component: () =>
      import(/* webpackChunkName: "cym" */ "../pages/exam/ExamGuide1.vue"),
    meta: { auth: true },
  },
  {
    path: "/exam/guide2",
    name: "ExamGuide2",
    component: () =>
      import(/* webpackChunkName: "cym" */ "../pages/exam/ExamGuide2.vue"),
    meta: { auth: true },
  },
  {
    path: "/exam/wait",
    name: "ExamWait",
    component: () =>
      import(/* webpackChunkName: "cym" */ "../pages/exam/ExamWait.vue"),
    meta: { auth: true },
  },
  {
    path: "/mypage",
    name: "Mypage",
    component: () =>
      import(/* webpackChunkName: "cym" */ "../pages/mypage/MypageMain.vue"),
    meta: { auth: true },
  },
  {
    path: "/mypage/settings",
    name: "Settings",
    component: () =>
      import(
        /* webpackChunkName: "cym" */ "../pages/mypage/Settings/Settings.vue"
      ),
    meta: { auth: true },
  },
  {
    path: "/mypage/settings/account",
    name: "AccountSettings",
    component: () =>
      import(
        /* webpackChunkName: "cym" */ "../pages/mypage/Settings/Account.vue"
      ),
    meta: { auth: true },
  },
  {
    path: "/mypage/settings/sns",
    name: "SnsSettings",
    component: () =>
      import(
        /* webpackChunkName: "cym" */ "../pages/mypage/Settings/SnsLogin.vue"
      ),
    meta: { auth: true },
  },
  {
    path: "/mypage/settings/alert",
    name: "AlertSettings",
    component: () =>
      import(
        /* webpackChunkName: "cym" */ "../pages/mypage/AlertSetting/AlertSettingMain.vue"
      ),
    meta: { auth: true },
  },
  {
    path: "/mypage/settings/version",
    name: "AppVersion",
    component: () =>
      import(
        /* webpackChunkName: "cym" */ "../pages/mypage/Settings/AppVersion.vue"
      ),
    meta: { auth: true },
  },
  {
    path: "/mypage/settings/opensource",
    name: "OpenSourceLicense",
    component: () =>
      import(
        /* webpackChunkName: "cym" */ "../pages/mypage/Settings/OpenSource.vue"
      ),
    meta: { auth: true },
  },
  {
    path: "/mypage/settings/delete",
    name: "DeleteUser",
    component: () =>
      import(
        /* webpackChunkName: "cym" */ "../pages/mypage/Settings/DeleteUser.vue"
      ),
    meta: { auth: true },
  },
  {
    path: "/mypage/guide",
    name: "UseageGuide",
    component: () =>
      import(
        /* webpackChunkName: "cym" */ "../pages/mypage/UserGuide/GuideView.vue"
      ),
    meta: { auth: true },
  },
  {
    path: "/mypage/guide/cym702",
    name: "AppGuide",
    component: () =>
      import(
        /* webpackChunkName: "cym" */ "../pages/mypage/UserGuide/AppGuide.vue"
      ),
    meta: { auth: true },
  },
  {
    path: "/mypage/guide/test",
    name: "ExamGuide",
    component: () =>
      import(
        /* webpackChunkName: "cym" */ "../pages/mypage/UserGuide/ExamGuide.vue"
      ),
    meta: { auth: true },
  },
  {
    path: "/mypage/examalert",
    name: "ExamAlertSetting",
    component: () =>
      import(
        /* webpackChunkName: "cym" */ "../pages/mypage/ExamAlert/ExamAlertSetting.vue"
      ),
    meta: { auth: true },
  },
  // 심 리포트 페이지
  {
    path: "/mypage/cym-report",
    name: "CymReport",
    component: () =>
      import(
        /* webpackChunkName: "cym" */ "../pages/mypage/report/Cym-report.page.vue"
      ),
    meta: { auth: true },
  },
  {
    path: "/solution",
    name: "Solution",
    component: () =>
      import(/* webpackChunkName: "cym" */ "../pages/solution/Solution.vue"),
    meta: { auth: true },
  },
  {
    path: "/solution/:id",
    name: "SolutionDetail",
    component: () =>
      import(
        /* webpackChunkName: "cym" */ "../pages/solution/subpage/SolutionDetail.vue"
      ),
    meta: { auth: true },
  },
  {
    path: "/food/search",
    name: "FoodSearch",
    component: () =>
      import(
        /* webpackChunkName: "cym" */ "../pages/solution/subpage/FoodSearchPage.vue"
      ),
    meta: { auth: true },
  },
  {
    path: "/food/edit/:id",
    name: "FoodEdit",
    component: () =>
      import(
        /* webpackChunkName: "cym" */ "../pages/solution/subpage/FoodEditPage.vue"
      ),
    meta: { auth: true },
  },
  {
    path: "/mypage/notice",
    name: "MypageNotice",
    component: () =>
      import(/* webpackChunkName: "cym" */ "../pages/mypage/Notice/Notice.vue"),
    meta: { auth: true },
  },
  {
    path: "/help",
    name: "CustomerService",
    component: () =>
      import(/* webpackChunkName: "cym" */ "../pages/mypage/Help/HelpMain.vue"),
    meta: { auth: true },
  },
  {
    path: "/help/faq",
    name: "Faq",
    component: () =>
      import(
        /* webpackChunkName: "cym" */ "../pages/mypage/Help/SubPages/Faq.vue"
      ),
    meta: { auth: true },
  },
  {
    path: "/help/terms/service",
    name: "ServiceTerms",
    component: () =>
      import(
        /* webpackChunkName: "cym" */ "../pages/mypage/Help/SubPages/TermsPages/Service.vue"
      ),
    meta: { auth: true },
  },
  {
    path: "/help/terms/privacy",
    name: "PrivacyTerms",
    component: () =>
      import(
        /* webpackChunkName: "cym" */ "../pages/mypage/Help/SubPages/TermsPages/Privacy.vue"
      ),
    meta: { auth: true },
  },
  {
    path: "/help/terms/marketing",
    name: "MarketingTerms",
    component: () =>
      import(
        /* webpackChunkName: "cym" */ "../pages/mypage/Help/SubPages/TermsPages/Marketing.vue"
      ),
    meta: { auth: true },
  },
  {
    path: "/help/terms/sensitive",
    name: "SensitiveTerms",
    component: () =>
      import(
        /* webpackChunkName: "cym" */ "../pages/mypage/Help/SubPages/TermsPages/Sensitive.vue"
      ),
    meta: { auth: true },
  },
  {
    path: "/profile",
    name: "Myprofile",
    component: () =>
      import(
        /* webpackChunkName: "cym" */ "../pages/mypage/Profile/ProfileMain.vue"
      ),
    meta: { auth: true },
  },
  {
    path: "/profile/name",
    name: "NameEdit",
    component: () =>
      import(
        /* webpackChunkName: "cym" */ "../pages/mypage/Profile/subpages/NameEdit.vue"
      ),
    meta: { auth: true },
  },
  {
    path: "/profile/phone",
    name: "PhoneEdit",
    component: () =>
      import(
        /* webpackChunkName: "cym" */ "../pages/mypage/Profile/subpages/PhoneEdit.vue"
      ),
    meta: { auth: true },
  },
  {
    path: "/profile/password",
    name: "PasswordEdit",
    component: () =>
      import(
        /* webpackChunkName: "cym" */ "../pages/mypage/Profile/subpages/PasswordEdit.vue"
      ),
    meta: { auth: true },
  },
  {
    path: "/profile/survey",
    name: "SurveyEdit",
    component: () =>
      import(
        /* webpackChunkName: "cym" */ "../pages/mypage/Profile/subpages/SurveyEdit.vue"
      ),
    meta: { auth: true },
  },
  {
    path: "/profile/edit",
    name: "ProfileEdit",
    component: () =>
      import(
        /* webpackChunkName: "cym" */ "../pages/mypage/Profile/subpages/BasicInfoEdit.vue"
      ),
    meta: { auth: true },
  },
  {
    path: "/profile/edit/sub",
    name: "SubProfileEdit",
    component: () =>
      import(
        /* webpackChunkName: "cym" */ "../pages/mypage/Profile/subpages/SubInfoEdit.vue"
      ),
    meta: { auth: true },
  },
  {
    path: "/subuser",
    name: "SubUserAdd",
    component: () =>
      import(
        /* webpackChunkName: "cym" */ "../pages/mypage/SubUser/AddUser.vue"
      ),
    meta: { auth: true },
  },
  {
    path: "/subprofile/:id",
    name: "SubProfile",
    component: () =>
      import(
        /* webpackChunkName: "cym" */ "../pages/mypage/Profile/SubProfile.vue"
      ),
    meta: { auth: true },
  },
];

const router = new VueRouter({
  mode: "history",
  scrollBehavior() {
    return { x: 0, y: 0 };
  },
  base: process.env.BASE_URL,
  routes,
});

router.beforeEach((to, from, next) => {
  const loggedIn = localStorage.getItem("auth");

  if (to.meta.auth && !loggedIn) {
    return next("/login");
  } else {
    next();
  }
});

export default router;
