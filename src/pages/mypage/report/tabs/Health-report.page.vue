<template>
  <section>
    <HealthReportProfile :selectedDate="selectedDate" />

    <SelectDateModal />
  </section>
</template>

<script>
import MockData from "./test.json";

import HealthReportProfile from "../../../../components/Mypage/report/features/health/health-report-profile.ui.vue";
import SelectDateModal from "../../../../components/Mypage/report/ui/select-date-modal.ui.vue";

export default {
  name: "HealthReport",

  components: {
    HealthReportProfile,
    SelectDateModal,
  },

  data() {
    return {
      selectedDate: "2025-07",
    };
  },

  mounted() {
    console.log(MockData);
  },

  methods: {
    getReportData() {
      try {
        const data = MockData;
        console.log(data);
      } catch (error) {
        console.error(error);
      }
    },
  },
};
</script>

<style lang="scss" scoped>
section {
  width: 100vw;
}
</style>
