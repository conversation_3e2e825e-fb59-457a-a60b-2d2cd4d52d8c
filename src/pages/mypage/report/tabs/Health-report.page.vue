<template>
  <section>
    <!-- 프로필 -->
    <HealthReportProfile
      :selectedDate="selectedDate"
      :profileData="profileData"
    />

    <DivideLine />

    <!-- 리포트 데이터 -->
    <ReportUrineTest :urineData="urineData" />

    <SelectDateModal />
  </section>
</template>

<script>
import MockData from "./test.json";

import HealthReportProfile from "../../../../components/Mypage/report/features/health/health-report-profile.ui.vue";
import ReportUrineTest from "../../../../components/Mypage/report/features/health/report-urine-test.ui.vue";
import SelectDateModal from "../../../../components/Mypage/report/ui/select-date-modal.ui.vue";
import DivideLine from "../../../../components/Mypage/report/ui/divide.ui.vue";

export default {
  name: "HealthReport",

  components: {
    HealthReportProfile,

    // 리포트 데이터 컴포넌트
    ReportUrineTest,
    SelectDateModal,
    DivideLine,
  },

  data() {
    return {
      selectedDate: "2024-02",
      profileData: {},
      urineData: {},
    };
  },

  mounted() {
    console.log(MockData);

    this.getReportData();
  },

  methods: {
    getReportData() {
      try {
        const data = MockData;

        console.log(data);

        this.profileData = {
          averageScore: data.averageScore,
          subject: data.subject,
        };

        this.urineData = {
          urineCount: {
            morningUrine: data.morningUrine,
            lunchUrine: data.lunchUrine,
            eveningUrine: data.eveningUrine,
          },
          urines: data.urine,
          isKetoneMode: data.ketoneMode,
        };
      } catch (error) {
        console.error(error);
      }
    },
  },
};
</script>

<style lang="scss" scoped>
section {
  width: 100%;
  padding-bottom: 80px;
}
</style>
