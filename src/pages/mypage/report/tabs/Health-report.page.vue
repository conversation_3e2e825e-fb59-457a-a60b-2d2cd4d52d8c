<template>
  <section>
    <HealthReportProfile />
    <SelectDateModal />
  </section>
</template>

<script>
import MockData from "./test.json";

import HealthReportProfile from "../../../../components/Mypage/report/features/health/health-report-profile.ui.vue";
import SelectDateModal from "../../../../components/Mypage/report/ui/select-date-modal.ui.vue";

export default {
  name: "HealthReport",

  components: {
    HealthReportProfile,
    SelectDateModal,
  },

  data() {
    return {};
  },

  mounted() {
    console.log(MockData);
  },
};
</script>

<style lang="scss" scoped></style>
