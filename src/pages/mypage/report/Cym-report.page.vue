<template>
  <background>
    <CymReportHeader />
    <ReportCategory :tab="currentTab" @change="handleChangeTab" />
    <v-tab-items v-model="currentTab"> </v-tab-items>
  </background>
</template>

<script>
import { defineComponent } from "vue";

import CymReportHeader from "../../../components/Mypage/report/ui/cym-report-header.ui.vue";
import ReportCategory from "../../../components/Mypage/report/ui/report-category.ui.vue";

import { CYM_REPORT_VARIABLE } from "./cym-report.enum";

export default defineComponent({
  name: "Cym-report",
  setup() {},

  components: {
    CymReportHeader,
    ReportCategory,
  },
  data() {
    return {
      currentTab: 0,
      tabItems: [
        {
          id: CYM_REPORT_VARIABLE.HEALTH_REPORT,
          value: CYM_REPORT_VARIABLE.HEALTH_REPORT,
        },
      ],
    };
  },
  methods: {
    handleChangeTab(newTab) {
      this.currentTab = newTab;
    },
  },
});
</script>

<style></style>
