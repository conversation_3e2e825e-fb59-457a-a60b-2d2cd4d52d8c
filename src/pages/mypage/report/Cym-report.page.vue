<template>
  <background>
    <CymReportHeader />
    <ReportCategory :tab="currentTab" @change="handleChangeTab" />
    <v-tab-items v-model="currentTab">
      <v-tab-item v-for="item in tabItems" :key="item.id" :value="item.value">
        <component :is="item.component" />
      </v-tab-item>
    </v-tab-items>
  </background>
</template>

<script>
import { defineComponent } from "vue";

import CymReportHeader from "../../../components/Mypage/report/ui/cym-report-header.ui.vue";
import ReportCategory from "../../../components/Mypage/report/ui/report-category.ui.vue";

import HealthReportPage from "./tabs/Health-report.page.vue";
import CareCalendarPage from "./tabs/Care-calendar.page.vue";

import { CYM_REPORT_VARIABLE } from "./cym-report.enum";

export default defineComponent({
  name: "Cym-report",
  setup() {},

  components: {
    CymReportHeader,
    ReportCategory,
    HealthReportPage,
    CareCalendarPage,
  },
  data() {
    return {
      currentTab: 0,
      tabItems: [
        {
          id: CYM_REPORT_VARIABLE.HEALTH_REPORT,
          value: CYM_REPORT_VARIABLE.HEALTH_REPORT,
          component: HealthReportPage,
        },
        {
          id: CYM_REPORT_VARIABLE.CARE_CALENDAR,
          value: CYM_REPORT_VARIABLE.CARE_CALENDAR,
          component: CareCalendarPage,
        },
      ],
    };
  },
  methods: {
    handleChangeTab(newTab) {
      this.currentTab = newTab;
    },
  },
});
</script>

<style></style>
