<template>
  <background>
    <!-- REPORT HEADER -->
    <CymReportHeader />

    <!-- REPORT CATEGORY -->
    <ReportCategory
      :tab="currentTab"
      @change="handleChangeTab"
      :tabList="tabs"
    />

    <!-- TAB ITEMS -->
    <div class="tab-content">
      <component
        v-for="item in tabItems"
        :key="item.id"
        :is="item.component"
        v-show="currentTab === item.value"
      />
    </div>

    <!-- NAVIGATION -->
    <Navigation :path="path" />
  </background>
</template>

<script>
import CymReportHeader from "../../../components/Mypage/report/ui/cym-report-header.ui.vue";
import ReportCategory from "../../../components/Mypage/report/ui/report-category.ui.vue";
import Navigation from "@/components/Common/Navigation.vue";

import HealthReportPage from "./tabs/Health-report.page.vue";
import CareCalendarPage from "./tabs/Care-calendar.page.vue";

import { CYM_REPORT_VARIABLE } from "./cym-report.enum";

export default {
  name: "Cym-report",
  setup() {},

  components: {
    CymReportHeader,
    ReportCategory,
    HealthReportPage,
    CareCalendarPage,
    Navigation,
  },
  data() {
    return {
      path: "/mypage/cym-report",
      currentTab: "#health-report",
      tabItems: [
        {
          id: CYM_REPORT_VARIABLE.HEALTH_REPORT,
          value: "health-report",
          component: HealthReportPage,
        },
        {
          id: CYM_REPORT_VARIABLE.CARE_CALENDAR,
          value: "care-calendar",
          component: CareCalendarPage,
        },
      ],
      tabs: [
        {
          id: CYM_REPORT_VARIABLE.HEALTH_REPORT,
          name: "건강리포트",
          href: "#health-report",
          component: HealthReportPage,
        },
        {
          id: CYM_REPORT_VARIABLE.CARE_CALENDAR,
          name: "케어 캘린더",
          href: "#care-calendar",
          component: CareCalendarPage,
        },
      ],
    };
  },
  methods: {
    handleChangeTab(newTab) {
      console.log("handleChangeTab called with:", newTab, typeof newTab);
      this.currentTab = newTab;
      console.log("currentTab is now:", this.currentTab);
    },
  },
};
</script>

<style></style>
