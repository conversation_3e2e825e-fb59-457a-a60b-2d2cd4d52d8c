<template>
  <background>
    <!-- REPORT HEADER -->
    <CymReportHeader />

    <!-- REPORT CATEGORY -->
    <ReportCategory
      :tab="currentTab"
      @change="handleChangeTab"
      :tabList="tabs"
    />

    <!-- TAB ITEMS -->
    <v-tab-items v-model="currentTab">
      <v-tab-item v-for="item in tabItems" :key="item.id" :value="item.value">
        <component :is="item.component" />
      </v-tab-item>
    </v-tab-items>

    <!-- NAVIGATION -->
    <Navigation :path="path" />
  </background>
</template>

<script>
import CymReportHeader from "../../../components/Mypage/report/ui/cym-report-header.ui.vue";
import ReportCategory from "../../../components/Mypage/report/ui/report-category.ui.vue";
import Navigation from "@/components/Common/Navigation.vue";

import HealthReportPage from "./tabs/Health-report.page.vue";
import CareCalendarPage from "./tabs/Care-calendar.page.vue";

import { CYM_REPORT_VARIABLE } from "./cym-report.enum";

export default {
  name: "Cym-report",
  setup() {},

  components: {
    CymReportHeader,
    ReportCategory,
    HealthReportPage,
    CareCalendarPage,
    Navigation,
  },
  data() {
    return {
      path: "/mypage/cym-report",
      currentTab: 0,
      tabItems: [
        {
          id: CYM_REPORT_VARIABLE.HEALTH_REPORT,
          value: CYM_REPORT_VARIABLE.HEALTH_REPORT,
          component: HealthReportPage,
        },
        {
          id: CYM_REPORT_VARIABLE.CARE_CALENDAR,
          value: CYM_REPORT_VARIABLE.CARE_CALENDAR,
          component: CareCalendarPage,
        },
      ],
      tabs: [
        {
          id: 0,
          name: "건강리포트",
          href: "#health-report",
          component: HealthReportPage,
        },
        {
          id: 1,
          name: "케어 캘린더",
          href: "#care-calendar",
          component: CareCalendarPage,
        },
      ],
    };
  },
  methods: {
    handleChangeTab(newTab) {
      this.currentTab = newTab;
    },
  },
};
</script>

<style></style>
